<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.file.mapper.DownloadRecordMapper">

    <select id="selectAllUsers" resultType="com.bxm.file.bean.dto.UserDTO">
        SELECT
            su.user_name as userName,
            su.nick_name as nickName,
            su.phonenumber as phonenumber,
            SUBSTRING_INDEX(SUBSTRING_INDEX(sd.ancestors, ',', 2), ',', -1) AS topDeptId
        FROM sys_user su LEFT JOIN sys_employee se ON su.user_id = se.user_id
                         LEFT JOIN sys_dept sd ON se.dept_id = sd.dept_id
        WHERE su.del_flag = '0' AND sd.del_flag = '0'
    </select>
    <select id="selectQualityCheckingItemByIds" resultType="com.bxm.file.bean.dto.QualityCheckingItemDTO">
        select
            id as qualityCheckingItemId,
            item_name as qualityCheckingItemName,
            quality_checking_type as qualityCheckingItemType,
            quality_checking_cycle as qualityCheckingItemCycle
            from c_quality_checking_item
        where `status` = 1
        <if test="qualityCheckingItemIds != null and qualityCheckingItemIds != ''">
            and id in (${qualityCheckingItemIds})
        </if>
    </select>
    <select id="selectAllQualityCheckingItem" resultType="com.bxm.file.bean.dto.QualityCheckingItemDTO">
        select
            id as qualityCheckingItemId,
            item_name as qualityCheckingItemName,
            quality_checking_type as qualityCheckingItemType,
            quality_checking_cycle as qualityCheckingItemCycle
        from c_quality_checking_item
        where `status` = 1
    </select>
</mapper>