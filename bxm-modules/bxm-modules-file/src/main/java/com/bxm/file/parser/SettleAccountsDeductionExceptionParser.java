package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAccountsDeductionExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAccountsDeductionExceptionParser implements ExcelV2Parser<SettleAccountsDeductionExceptionData> {

    @Override
    public List<SettleAccountsDeductionExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAccountsDeductionExceptionData.class);
    }
}
