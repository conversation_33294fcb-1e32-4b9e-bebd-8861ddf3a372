package com.bxm.file.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.file.bean.dto.*;
import com.bxm.file.bean.dto.rpa.*;
import com.bxm.file.bean.vo.RpaCreateVO;
import com.bxm.file.domain.CustomerRpaDetailFile;
import com.bxm.file.domain.CustomerRpaRecord;
import com.bxm.file.domain.CustomerRpaRecordDetail;
import com.bxm.file.util.ExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Service
@Slf4j
public class RpaFileService {

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICustomerRpaRecordService customerRpaRecordService;

    @Autowired
    private ICustomerRpaRecordDetailService customerRpaRecordDetailService;

    @Autowired
    private OssService ossService;

    @Autowired
    private ICustomerRpaDetailFileService customerRpaDetailFileService;

    public String checkCreateRpa(MultipartFile dataFile, MultipartFile zipFile, MultipartFile copyZipFile, RpaCreateVO vo) {
        if (null == dataFile || dataFile.isEmpty()) {
            throw new ServiceException("请上传数据文件");
        }
        if (!Objects.equals(StringUtils.getFileType(dataFile.getOriginalFilename()), "xlsx")) {
            throw new ServiceException("数据文件仅支持xlsx格式");
        }
        if (vo.getRpaType() != 4) {
            if (null == zipFile || zipFile.isEmpty()) {
                throw new ServiceException("请上传附件文件");
            }
            if (!Objects.equals(StringUtils.getFileType(zipFile.getOriginalFilename()), "zip") &&
                    !Objects.equals(StringUtils.getFileType(zipFile.getOriginalFilename()), "rar")) {
                throw new ServiceException("附件文件仅支持rar和zip格式");
            }
        }
        switch (vo.getRpaType()) {
            case 1:
                // 医社保
                break;
            case 2:
                // 个税（工资薪金）
                try {
                    ExcelUtil<PersonTaxImportDTO> util = new ExcelUtil<>(PersonTaxImportDTO.class);
                    List<PersonTaxImportDTO> personTaxList = util.importExcel(dataFile.getInputStream());
                    if (ObjectUtils.isEmpty(personTaxList)) {
                        throw new ServiceException("表内数据为空");
                    }
                    Long totalFileCount = 0L;
                    Map<Integer, PersonTaxImportDTO> map = personTaxList.stream().collect(Collectors.toMap(PersonTaxImportDTO::getNumber, v -> v, (k1, k2) -> k1));
                    try (ZipInputStream totalZipIn = new ZipInputStream(zipFile.getInputStream(), Charset.forName("GBK"))) {
                        ZipEntry entry;
                        while ((entry = totalZipIn.getNextEntry()) != null) {
                            if (!entry.isDirectory()) {
                                String fileName = entry.getName();
                                String attachFileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                                String numberStr = attachFileName.split("-")[0];
                                if (fileName.indexOf("/") != fileName.lastIndexOf("/") && StringUtils.isNumeric(numberStr)) {
                                    Integer number = Integer.parseInt(numberStr);
                                    PersonTaxImportDTO personTaxImportDTO = map.get(number);
                                    if (!Objects.isNull(personTaxImportDTO)) {
                                        totalFileCount++;
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("上传zip文件异常", e);
                    }
                    String batchNo = UUID.randomUUID().toString().replace("-", "");
                    redisService.setCacheObject(CacheConstants.RPA_TOTAL_DATA_COUNT + batchNo, personTaxList.size(), 60 * 60L, TimeUnit.SECONDS);
                    redisService.setCacheObject(CacheConstants.RPA_TOTAL_FILE_COUNT + batchNo, totalFileCount, 60 * 60L, TimeUnit.SECONDS);
                    redisService.setCacheObject(CacheConstants.RPA_TYPE + batchNo, vo.getRpaType(), 60 * 60L, TimeUnit.SECONDS);
                    fileUploadService.uploadRpaZipFile(personTaxList, copyZipFile, batchNo);
                    return batchNo;
                } catch (ServiceException e) {
                    throw new ServiceException(e.getMessage());
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceException("源文件读取异常！");
                }
            case 3:
                // 国税
                break;
            case 4:
                // 收入
                try {
                    ExcelUtil<CustomerPeriodIncomeDTO> util = new ExcelUtil<>(CustomerPeriodIncomeDTO.class);
                    List<CustomerPeriodIncomeDTO> periodIncomes = util.importExcel(dataFile.getInputStream());
                    if (ObjectUtils.isEmpty(periodIncomes)) {
                        throw new ServiceException("表内数据为空");
                    }
                    Long totalFileCount = (long) periodIncomes.size();
                    String batchNo = UUID.randomUUID().toString().replace("-", "");
                    redisService.setCacheObject(CacheConstants.RPA_TOTAL_DATA_COUNT + batchNo, totalFileCount, 60 * 60L, TimeUnit.SECONDS);
                    redisService.setCacheObject(CacheConstants.RPA_TOTAL_FILE_COUNT + batchNo, totalFileCount, 60 * 60L, TimeUnit.SECONDS);
                    fileUploadService.uploadPeriodIncome(periodIncomes, batchNo);
                    return batchNo;
                } catch (ServiceException e) {
                    throw new ServiceException(e.getMessage());
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceException("源文件读取异常！");
                }
            case 5:
                // 预认证
                break;
            default:
                throw new ServiceException("交付类型错误");
        }
        return null;
    }

    public RpaCreateResultDTO getCheckResult(String batchNo) {
        if (redisService.hasKey(CacheConstants.RPA_CHECK_RESULT + batchNo)) {
            RpaCreateResultDTO dto = redisService.getCacheObject(CacheConstants.RPA_CHECK_RESULT + batchNo);
            dto.setIsComplete(Boolean.TRUE);
            Long totalFileCount = redisService.getCacheObject(CacheConstants.RPA_TOTAL_FILE_COUNT + batchNo);
            Long completeFileCount = redisService.getCacheObject(CacheConstants.RPA_COMPLETE_FILE_COUNT + batchNo);
            dto.setTotalFileCount(Objects.isNull(totalFileCount) ? 0L : totalFileCount);
            dto.setCompleteFileCount(Objects.isNull(completeFileCount) ? 0L : completeFileCount);
            return dto;
        } else {
            Long totalFileCount = redisService.getCacheObject(CacheConstants.RPA_TOTAL_FILE_COUNT + batchNo);
            Long completeFileCount = redisService.getCacheObject(CacheConstants.RPA_COMPLETE_FILE_COUNT + batchNo);
            return RpaCreateResultDTO.builder().isComplete(Boolean.FALSE)
                    .batchNo(batchNo)
                    .completeFileCount(Objects.isNull(completeFileCount) ? 0L : completeFileCount)
                    .totalFileCount(Objects.isNull(totalFileCount) ? 0L : totalFileCount)
                    .build();
        }
    }

    @Transactional
    public void savePersonTaxRpaRecord(RpaCreateVO vo) {
        if (StringUtils.isEmpty(vo.getBatchNo())) {
            throw new ServiceException("批次号不能为空");
        }
        if (!redisService.hasKey(CacheConstants.RPA_CHECK_RESULT + vo.getBatchNo())) {
            throw new ServiceException("校验数据未完成");
        }
        List<PersonTaxImportDTO> originList = redisService.getCacheList(CacheConstants.RPA_ORIGIN_LIST + vo.getBatchNo());
        if (ObjectUtils.isEmpty(originList)) {
            throw new ServiceException("初始数据为空");
        }
        RpaCreateResultDTO dto = redisService.getCacheObject(CacheConstants.RPA_CHECK_RESULT + vo.getBatchNo());
        CustomerRpaRecord record = new CustomerRpaRecord()
                .setRpaType(vo.getRpaType())
                .setOperType(vo.getOperType())
                .setBatchNo(vo.getBatchNo())
                .setPeriod(vo.getPeriod())
                .setDeptId(vo.getDeptId())
                .setStatus(1)
                .setHasErrorData(dto.getHasException())
                .setTotalDataCount((long) originList.size())
                .setImportDataCount(originList.stream().filter(r -> !StringUtils.isEmpty(r.getExceptionReason())).count());
        customerRpaRecordService.save(record);
        customerRpaRecordDetailService.savePersonTaxDetails(record.getId(), originList);
    }

    public void savePeriodIncomeRpaRecord(RpaCreateVO vo) {
        if (StringUtils.isEmpty(vo.getBatchNo())) {
            throw new ServiceException("批次号不能为空");
        }
        if (!redisService.hasKey(CacheConstants.RPA_CHECK_RESULT + vo.getBatchNo())) {
            throw new ServiceException("校验数据未完成");
        }
        List<CustomerPeriodIncomeDTO> originList = redisService.getCacheList(CacheConstants.RPA_ORIGIN_LIST + vo.getBatchNo());
        if (ObjectUtils.isEmpty(originList)) {
            throw new ServiceException("初始数据为空");
        }
        RpaCreateResultDTO dto = redisService.getCacheObject(CacheConstants.RPA_CHECK_RESULT + vo.getBatchNo());
        CustomerRpaRecord record = new CustomerRpaRecord()
                .setRpaType(vo.getRpaType())
                .setOperType(vo.getOperType())
                .setBatchNo(vo.getBatchNo())
                .setPeriod(vo.getPeriod())
                .setDeptId(vo.getDeptId())
                .setStatus(1)
                .setHasErrorData(dto.getHasException())
                .setTotalDataCount((long) originList.size())
                .setImportDataCount(originList.stream().filter(r -> !StringUtils.isEmpty(r.getExceptionReason())).count());
        customerRpaRecordService.save(record);
        customerRpaRecordDetailService.savePeriodIncomeDetails(record.getId(), originList);
    }

    public String createCheck(Integer rpaType, Integer operType, MultipartFile excelFile, MultipartFile zipFile, Integer period, Long queryDeptId, Long deptId) throws Exception {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        if ((rpaType == 1 && operType != 1) || (rpaType == 2 && operType != 1 && operType != 4) || (rpaType == 3 && operType != 1 && operType != 2 && operType != 4) || (rpaType == 4 && operType != 3) || (rpaType == 5 && operType != 3)) {
            throw new ServiceException("rpa类型与操作类型不支持");
        }
        Boolean isZip = checkArchiveType(zipFile);
        List<RpaEnterpriseData> dataList = Lists.newArrayList();
        try {
            switch (rpaType) {
                case 1:
                    ExcelUtil<RpaMedicalSocialInsuranceReportSheet2Data> util = new ExcelUtil<>(RpaMedicalSocialInsuranceReportSheet2Data.class);
                    Map<String, List<RpaMedicalSocialInsuranceReportSheet2Data>> map = util.importSheetExcel(excelFile.getInputStream(), 0);
                    List<RpaMedicalSocialInsuranceReportSheet2Data> sheet1List = map.get("0");
                    List<RpaMedicalSocialInsuranceReportSheet2Data> sheet2List = map.get("1");
                    if (!ObjectUtils.isEmpty(sheet1List)) {
                        List<RpaMedicalSocialInsuranceReportSheet1Data> sheet1DataList = sheet1List.stream().map(r -> {
                            RpaMedicalSocialInsuranceReportSheet1Data sheet1Data = new RpaMedicalSocialInsuranceReportSheet1Data();
                            BeanUtils.copyProperties(r, sheet1Data);
                            sheet1Data.setSheetIndex("1");
                            return sheet1Data;
                        }).collect(Collectors.toList());
                        dataList.addAll(sheet1DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                    }
                    if (!ObjectUtils.isEmpty(sheet2List)) {
                        sheet2List.forEach(d -> d.setSheetIndex("2"));
                        dataList.addAll(sheet2List.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                    }
                    break;
                case 2:
                case 6:
                    List<RpaPersonTaxReportData> data = ExcelUtils.parseExcelFile(excelFile, RpaPersonTaxReportData.class);
                    if (!ObjectUtils.isEmpty(data)) {
                        dataList.addAll(data.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                    }
                    break;
                case 3:
                    if (operType == 1) {
                        ExcelUtil<RpaCountryTaxReportSheet2Data> util1 = new ExcelUtil<>(RpaCountryTaxReportSheet2Data.class);
                        Map<String, List<RpaCountryTaxReportSheet2Data>> map1 = util1.importSheetExcel(excelFile.getInputStream(), 0);
                        List<RpaCountryTaxReportSheet2Data> sheet1List1 = map1.get("0");
                        List<RpaCountryTaxReportSheet2Data> sheet2List1 = map1.get("1");
                        if (!ObjectUtils.isEmpty(sheet1List1)) {
                            List<RpaCountryTaxReportSheet1Data> sheet1DataList = sheet1List1.stream().map(r -> {
                                RpaCountryTaxReportSheet1Data sheet1Data = new RpaCountryTaxReportSheet1Data();
                                BeanUtils.copyProperties(r, sheet1Data);
                                sheet1Data.setSheetIndex("1");
                                return sheet1Data;
                            }).collect(Collectors.toList());
                            dataList.addAll(sheet1DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                        if (!ObjectUtils.isEmpty(sheet2List1)) {
                            sheet2List1.forEach(d -> d.setSheetIndex("2"));
                            dataList.addAll(sheet2List1.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                    } else if (operType == 2) {
                        ExcelUtil<RpaCountryTaxDeductionSheet2Data> util2 = new ExcelUtil<>(RpaCountryTaxDeductionSheet2Data.class);
                        Map<String, List<RpaCountryTaxDeductionSheet2Data>> map1 = util2.importSheetExcel(excelFile.getInputStream(), 0);
                        List<RpaCountryTaxDeductionSheet2Data> sheet1List2 = map1.get("0");
                        List<RpaCountryTaxDeductionSheet2Data> sheet2List2 = map1.get("1");
                        if (!ObjectUtils.isEmpty(sheet1List2)) {
                            List<RpaCountryTaxDeductionSheet1Data> sheet1DataList = sheet1List2.stream().map(r -> {
                                RpaCountryTaxDeductionSheet1Data sheet1Data = new RpaCountryTaxDeductionSheet1Data();
                                BeanUtils.copyProperties(r, sheet1Data);
                                sheet1Data.setSheetIndex("1");
                                return sheet1Data;
                            }).collect(Collectors.toList());
                            dataList.addAll(sheet1DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                        if (!ObjectUtils.isEmpty(sheet2List2)) {
                            sheet2List2.forEach(d -> d.setSheetIndex("2"));
                            dataList.addAll(sheet2List2.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                    } else {
                        ExcelUtil<RpaCountryTaxCheckResultSheetData> checkResultUtil = new ExcelUtil<>(RpaCountryTaxCheckResultSheetData.class);
                        Map<String, List<RpaCountryTaxCheckResultSheetData>> checkResultMap = checkResultUtil.importSheetExcel(excelFile.getInputStream(), 0);
                        List<RpaCountryTaxCheckResultSheetData> checkResukltSheet1List = checkResultMap.get("0");
                        List<RpaCountryTaxCheckResultSheetData> checkResukltSheet2List = checkResultMap.get("1");
                        if (!ObjectUtils.isEmpty(checkResukltSheet1List)) {
                            List<RpaCountryTaxCheckResultSheet1Data> sheet1DataList = checkResukltSheet1List.stream().map(r -> {
                                RpaCountryTaxCheckResultSheet1Data sheet1Data = new RpaCountryTaxCheckResultSheet1Data();
                                BeanUtils.copyProperties(r, sheet1Data);
                                sheet1Data.setSheetIndex("1");
                                return sheet1Data;
                            }).collect(Collectors.toList());
                            dataList.addAll(sheet1DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                        if (!ObjectUtils.isEmpty(checkResukltSheet2List)) {
                            List<RpaCountryTaxCheckResultSheet2Data> sheet2DataList = checkResukltSheet2List.stream().map(r -> {
                                RpaCountryTaxCheckResultSheet2Data sheet2Data = new RpaCountryTaxCheckResultSheet2Data();
                                BeanUtils.copyProperties(r, sheet2Data);
                                sheet2Data.setSheetIndex("2");
                                return sheet2Data;
                            }).collect(Collectors.toList());
                            dataList.addAll(sheet2DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                        }
                    }
                    break;
                case 4:
                    break;
                case 5:
                    ExcelUtil<RpaCustomerPeriodIncomeSheet2Data> util3 = new ExcelUtil<>(RpaCustomerPeriodIncomeSheet2Data.class);
                    Map<String, List<RpaCustomerPeriodIncomeSheet2Data>> map3 = util3.importSheetExcel(excelFile.getInputStream(), 0);
                    List<RpaCustomerPeriodIncomeSheet2Data> sheet1List3 = map3.get("0");
                    List<RpaCustomerPeriodIncomeSheet2Data> sheet2List3 = map3.get("1");
                    if (!ObjectUtils.isEmpty(sheet1List3)) {
                        List<RpaCustomerPeriodIncomeSheet1Data> sheet1DataList = sheet1List3.stream().map(r -> {
                            RpaCustomerPeriodIncomeSheet1Data sheet1Data = new RpaCustomerPeriodIncomeSheet1Data();
                            BeanUtils.copyProperties(r, sheet1Data);
                            sheet1Data.setSheetIndex("1");
                            return sheet1Data;
                        }).collect(Collectors.toList());
                        dataList.addAll(sheet1DataList.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                    }
                    if (!ObjectUtils.isEmpty(sheet2List3)) {
                        sheet2List3.forEach(d -> d.setSheetIndex("2"));
                        dataList.addAll(sheet2List3.stream().map(r -> (RpaEnterpriseData) r).collect(Collectors.toList()));
                    }
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("数据解析异常");
        }

        if (!ObjectUtils.isEmpty(dataList)) {
            dataList.forEach(d -> {
                d.setCheckError("");
                d.setCreditCode(StringUtils.trim(d.getCreditCode()));
            });
        }

        // 将 zip 文件转换为 byte 数组
        byte[] zipBytes = convertToByteArray(zipFile);
        Long userId = SecurityUtils.getUserId();

        // 异步校验和上传数据
        CompletableFuture.runAsync(() -> {
            try {
                fileUploadService.validateAndUploadRpaData(uuid, dataList, zipBytes, period, queryDeptId, userId, operType, rpaType, isZip);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return uuid;
    }

    private Boolean checkArchiveType(MultipartFile zipFile) {
        Boolean isZip = null;
        if (null != zipFile) {
            String fileName = zipFile.getOriginalFilename();
            if (fileName.endsWith(".zip")) {
                isZip = true;
            } else if (fileName.endsWith(".rar")) {
                isZip = false;
            } else {
                throw new ServiceException("附件只支持zip或rar格式");
            }
        }
        return isZip;
    }

    private byte[] convertToByteArray(MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            return null;
        }
        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

    public RpaCheckResult getProgress(String batchNo) {
        RpaCheckResult checkResult = redisService.getCacheObject(CacheConstants.RPA_CHECK_RESULT + batchNo);
        if (Objects.isNull(checkResult)) {
            checkResult = new RpaCheckResult();
            checkResult.setIsComplete(false);
            Object completeCount = redisService.getCacheObject(CacheConstants.RPA_COMPLETE_FILE_COUNT + batchNo);
            Object totalCount = redisService.getCacheObject(CacheConstants.RPA_TOTAL_FILE_COUNT + batchNo);
            checkResult.setCompleteFileCount(Objects.isNull(completeCount) ? 0L : Long.parseLong(completeCount.toString()));
            checkResult.setTotalFileCount(Objects.isNull(totalCount) ? 0L : Long.parseLong(totalCount.toString()));
            checkResult.setBatchNo(batchNo);
        }
        return checkResult;
    }

    public void confirmData(String batchNo) {
        RpaCheckResult checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据解析未完成");
        }
        List<? extends RpaEnterpriseData> dataList = checkResult.getDataList();
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        CustomerRpaRecord record = new CustomerRpaRecord()
                .setRpaType(checkResult.getRpaType())
                .setOperType(checkResult.getOperType())
                .setBatchNo(checkResult.getBatchNo())
                .setPeriod(checkResult.getPeriod())
                .setDeptId(checkResult.getDeptId())
                .setStatus(1)
                .setHasErrorData(checkResult.getHasException())
                .setTotalDataCount((long) checkResult.getDataList().size())
                .setImportDataCount(0L);
        customerRpaRecordService.save(record);
        customerRpaRecordDetailService.saveRpaRecordDetails(record.getId(), record.getRpaType(), record.getOperType(), checkResult.getDataList());
    }

    private String invokeGetMethod(RpaEnterpriseData data, String methodName) {
        try {
            Method method = data.getClass().getMethod(methodName);
            return (String) method.invoke(data);
        } catch (Exception e) {
            return null;
        }
    }

    public void downloadErrorFile(String batchNo, HttpServletResponse response) throws IOException{
        RpaCheckResult checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据解析未完成");
        }
        List<? extends RpaEnterpriseData> dataList = checkResult.getDataList();
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        List<RpaEnterpriseData> errorList = dataList.stream().filter(RpaEnterpriseData::hasErrors).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(errorList)) {
            throw new ServiceException("暂无异常数据下载");
        }
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        Map<String, List<?>> dataMap = new HashMap<>();
        switch (checkResult.getRpaType()) {
            case 1:
                // 医社保
                sheetClassMap.put("办税员登入", RpaMedicalSocialInsuranceReportSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaMedicalSocialInsuranceReportSheet2Data.class);
                dataMap.put("办税员登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ExcelUtils.exportExcel(dataMap, sheetClassMap, "异常数据_医社保_申报_" + checkResult.getPeriod(), response);
                break;
            case 2:
                // 个税（工资薪金）
                if (checkResult.getOperType() == 1) {
                    ExcelUtil<RpaPersonTaxReportData> util = new ExcelUtil<>(RpaPersonTaxReportData.class);
                    List<RpaPersonTaxReportData> personErrorList = errorList.stream().map(d -> (RpaPersonTaxReportData) d).collect(Collectors.toList());
                    util.exportExcel(response, personErrorList, "异常数据_个税（工资薪金）_申报_" + checkResult.getPeriod());
                } else {
                    ExcelUtil<RpaPersonTaxReportData> util = new ExcelUtil<>(RpaPersonTaxReportData.class);
                    List<RpaPersonTaxReportData> personErrorList = errorList.stream().map(d -> (RpaPersonTaxReportData) d).collect(Collectors.toList());
                    util.exportExcel(response, personErrorList, "异常数据_个税（工资薪金）_检查申报结果_" + checkResult.getPeriod());
                }
                break;
            case 3:
                // 国税
                if (checkResult.getOperType() == 1) {
                    sheetClassMap.put("办税员登入", RpaCountryTaxReportSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxReportSheet2Data.class);
                    dataMap.put("办税员登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "异常数据_国税_申报_" + checkResult.getPeriod(), response);
                } else if (checkResult.getOperType() == 2) {
                    sheetClassMap.put("办税员登入", RpaCountryTaxDeductionSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxDeductionSheet2Data.class);
                    dataMap.put("办税员登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "异常数据_国税_扣款_" + checkResult.getPeriod(), response);
                } else {
                    sheetClassMap.put("办税员登入", RpaCountryTaxCheckResultSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxCheckResultSheet2Data.class);
                    dataMap.put("办税员登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "异常数据_国税_检查申报结果_" + checkResult.getPeriod(), response);
                }
                break;
            case 4:
                // 预认证
                break;
            case 5:
                // 收入
                sheetClassMap.put("办税员登入", RpaCustomerPeriodIncomeSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaCustomerPeriodIncomeSheet2Data.class);
                dataMap.put("办税员登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", errorList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ExcelUtils.exportExcel(dataMap, sheetClassMap, "异常数据_收入_" + checkResult.getPeriod(), response);
                break;
            default:
                break;
        }
    }

    public void downloadRpaRecordFile(Long rpaRecordId, HttpServletResponse response) throws Exception {
        CustomerRpaRecord record = customerRpaRecordService.getById(rpaRecordId);
        if (Objects.isNull(record)) {
            throw new ServiceException("暂无文件下载");
        }
        List<CustomerRpaRecordDetail> details = customerRpaRecordDetailService.selectByRpaRecordId(rpaRecordId);
        if (ObjectUtils.isEmpty(details)) {
            throw new ServiceException("暂无文件下载");
        }
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        Map<String, List<?>> dataMap = new HashMap<>();
        switch (record.getRpaType()) {
            case 1:
                List<RpaMedicalSocialInsuranceReportSheet2Data> type1SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaMedicalSocialInsuranceReportSheet2Data.class)).collect(Collectors.toList());
                sheetClassMap.put("办税员登入", RpaMedicalSocialInsuranceReportSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaMedicalSocialInsuranceReportSheet2Data.class);
                dataMap.put("办税员登入", type1SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                    RpaMedicalSocialInsuranceReportSheet1Data data = new RpaMedicalSocialInsuranceReportSheet1Data();
                    BeanUtils.copyProperties(d, data);
                    return data;
                }).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", type1SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ExcelUtils.exportExcel(dataMap, sheetClassMap, "医社保申报数据", response);
                break;
            case 2:
                // 个税（工资薪金）
                if (record.getOperType() == 1) {
                    ExcelUtil<RpaPersonTaxReportData> util = new ExcelUtil<>(RpaPersonTaxReportData.class);
                    List<RpaPersonTaxReportData> personErrorList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class)).collect(Collectors.toList());
                    util.exportExcel(response, personErrorList, "个税（工资薪金）申报数据");
                } else {
                    ExcelUtil<RpaPersonTaxReportData> util = new ExcelUtil<>(RpaPersonTaxReportData.class);
                    List<RpaPersonTaxReportData> personErrorList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class)).collect(Collectors.toList());
                    util.exportExcel(response, personErrorList, "个税（工资薪金）检查申报结果数据");
                }
                break;
            case 3:
                // 国税
                if (record.getOperType() == 1) {
                    List<RpaCountryTaxReportSheet2Data> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxReportSheet2Data.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxReportSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxReportSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxReportSheet1Data data = new RpaCountryTaxReportSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "国税申报数据", response);
                } else if (record.getOperType() == 2) {
                    List<RpaCountryTaxDeductionSheet2Data> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxDeductionSheet2Data.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxDeductionSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxDeductionSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxDeductionSheet1Data data = new RpaCountryTaxDeductionSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "国税扣款数据", response);
                } else {
                    List<RpaCountryTaxCheckResultSheetData> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxCheckResultSheetData.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxCheckResultSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxCheckResultSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxCheckResultSheet1Data data = new RpaCountryTaxCheckResultSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).map(d -> {
                        RpaCountryTaxCheckResultSheet2Data data = new RpaCountryTaxCheckResultSheet2Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    ExcelUtils.exportExcel(dataMap, sheetClassMap, "国税检查申报结果数据", response);
                }
                break;
            case 4:
                // 预认证
                break;
            case 5:
                // 收入
                List<RpaCustomerPeriodIncomeSheet2Data> type5SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCustomerPeriodIncomeSheet2Data.class)).collect(Collectors.toList());
                sheetClassMap.put("办税员登入", RpaCustomerPeriodIncomeSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaCustomerPeriodIncomeSheet2Data.class);
                dataMap.put("办税员登入", type5SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                    RpaCustomerPeriodIncomeSheet1Data data = new RpaCustomerPeriodIncomeSheet1Data();
                    BeanUtils.copyProperties(d, data);
                    return data;
                }).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", type5SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ExcelUtils.exportExcel(dataMap, sheetClassMap, "收入数据", response);
                break;
            default:
                break;
        }
    }

    public void downloadRpaDetailFile(Long rpaRecordId, String customerName, Integer status, HttpServletResponse response) throws Exception {
        CustomerRpaRecord record = customerRpaRecordService.getById(rpaRecordId);
        if (Objects.isNull(record)) {
            throw new ServiceException("暂无文件下载");
        }
        List<CustomerRpaRecordDetail> details = customerRpaRecordDetailService.list(new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId)
                .like(!StringUtils.isEmpty(customerName), CustomerRpaRecordDetail::getCustomerName, customerName)
                .eq(!Objects.isNull(status), CustomerRpaRecordDetail::getStatus, status));
        if (ObjectUtils.isEmpty(details)) {
            throw new ServiceException("暂无文件下载");
        }
        List<CustomerRpaDetailFile> files = customerRpaDetailFileService.selectBatchByRpaDetailId(details.stream().map(CustomerRpaRecordDetail::getId).collect(Collectors.toList()));
        List<String> fileKeys = ObjectUtils.isEmpty(files) ? Lists.newArrayList() :
                files.stream().map(CustomerRpaDetailFile::getFileUrl).distinct().collect(Collectors.toList());
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        Map<String, List<?>> dataMap = new HashMap<>();
        switch (record.getRpaType()) {
            case 1:
                List<RpaMedicalSocialInsuranceReportSheet2Data> type1SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaMedicalSocialInsuranceReportSheet2Data.class)).collect(Collectors.toList());
                sheetClassMap.put("办税员登入", RpaMedicalSocialInsuranceReportSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaMedicalSocialInsuranceReportSheet2Data.class);
                dataMap.put("办税员登入", type1SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                    RpaMedicalSocialInsuranceReportSheet1Data data = new RpaMedicalSocialInsuranceReportSheet1Data();
                    BeanUtils.copyProperties(d, data);
                    return data;
                }).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", type1SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "medicalSocial", response);
                break;
            case 2:
                // 个税（工资薪金）
                if (record.getOperType() == 1) {
                    sheetClassMap.put("个税（工资薪金）申报数据", RpaPersonTaxReportData.class);
                    dataMap.put("个税（工资薪金）申报数据", details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class)).collect(Collectors.toList()));
                    ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "personTax", response);
                } else {
                    sheetClassMap.put("个税（工资薪金）检查申报结果数据", RpaPersonTaxReportData.class);
                    dataMap.put("个税（工资薪金）检查申报结果数据", details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class)).collect(Collectors.toList()));
                    ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "personTax", response);
                }
                break;
            case 3:
                // 国税
                if (record.getOperType() == 1) {
                    List<RpaCountryTaxReportSheet2Data> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxReportSheet2Data.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxReportSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxReportSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxReportSheet1Data data = new RpaCountryTaxReportSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "countryTaxReport", response);
                } else if (record.getOperType() == 2) {
                    List<RpaCountryTaxDeductionSheet2Data> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxDeductionSheet2Data.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxDeductionSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxDeductionSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxDeductionSheet1Data data = new RpaCountryTaxDeductionSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                    ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "countryTaxDeduction", response);
                } else {
                    List<RpaCountryTaxCheckResultSheetData> type3SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCountryTaxCheckResultSheetData.class)).collect(Collectors.toList());
                    sheetClassMap.put("办税员登入", RpaCountryTaxCheckResultSheet1Data.class);
                    sheetClassMap.put("涉税企业登入", RpaCountryTaxCheckResultSheet2Data.class);
                    dataMap.put("办税员登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                        RpaCountryTaxCheckResultSheet1Data data = new RpaCountryTaxCheckResultSheet1Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    dataMap.put("涉税企业登入", type3SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).map(d -> {
                        RpaCountryTaxCheckResultSheet2Data data = new RpaCountryTaxCheckResultSheet2Data();
                        BeanUtils.copyProperties(d, data);
                        return data;
                    }).collect(Collectors.toList()));
                    ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "countryTaxCheckResult", response);
                }
                break;
            case 4:
                // 预认证
                break;
            case 5:
                // 收入
                List<RpaCustomerPeriodIncomeSheet2Data> type5SheetList = details.stream().map(d -> JSONObject.parseObject(d.getContent(), RpaCustomerPeriodIncomeSheet2Data.class)).collect(Collectors.toList());
                sheetClassMap.put("办税员登入", RpaCustomerPeriodIncomeSheet1Data.class);
                sheetClassMap.put("涉税企业登入", RpaCustomerPeriodIncomeSheet2Data.class);
                dataMap.put("办税员登入", type5SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "1")).map(d -> {
                    RpaCustomerPeriodIncomeSheet1Data data = new RpaCustomerPeriodIncomeSheet1Data();
                    BeanUtils.copyProperties(d, data);
                    return data;
                }).collect(Collectors.toList()));
                dataMap.put("涉税企业登入", type5SheetList.stream().filter(d -> Objects.equals(d.getSheetIndex(), "2")).collect(Collectors.toList()));
                ExcelUtils.exportExcel(dataMap, sheetClassMap, "收入数据", response);
                ossService.downloadFilesAndGenerateZip(fileKeys, dataMap, sheetClassMap, "income", response);
                break;
            default:
                break;
        }
    }

    public void downloadRpaRecordAttachment(Long rpaRecordId, HttpServletResponse response) throws Exception {
        CustomerRpaRecord record = customerRpaRecordService.getById(rpaRecordId);
        if (Objects.isNull(record)) {
            throw new ServiceException("暂无文件下载");
        }
        List<CustomerRpaRecordDetail> details = customerRpaRecordDetailService.list(new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId));
        if (ObjectUtils.isEmpty(details)) {
            throw new ServiceException("暂无文件下载");
        }
        List<CustomerRpaDetailFile> files = customerRpaDetailFileService.selectBatchByRpaDetailId(details.stream().map(CustomerRpaRecordDetail::getId).collect(Collectors.toList()));
        List<String> fileKeys = ObjectUtils.isEmpty(files) ? Lists.newArrayList() :
                files.stream().map(CustomerRpaDetailFile::getFileUrl).distinct().collect(Collectors.toList());
        ossService.downloadFilesAndGenerateZip(fileKeys, null, null, "income", response);
    }
}
