package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.file.bean.dto.AliFileDTO;
import com.bxm.file.bean.vo.ThirdpartFileVO;
import com.bxm.file.service.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController
@RequestMapping("/fileUpload")
@Api(tags = "文件上传oss")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    @PostMapping("/commonUpload")
    @ApiOperation("统一文件上传")
    public Result<AliFileDTO> upload(@RequestParam("file") MultipartFile file) {
        return Result.ok(fileUploadService.uploadAliOss(file));
    }

    @GetMapping("/deleteOssFile")
    @InnerAuth
    @ApiIgnore
    public Result deleteOssFile(@RequestParam("url") String url) {
        fileUploadService.deleteOssFile(url);
        return Result.ok();
    }

    @PostMapping("/uploadLargeFile")
    @ApiOperation("大文件上传")
    public Result<AliFileDTO> uploadLargeFile(@RequestParam("file") MultipartFile file) {
        return Result.ok(fileUploadService.uploadLargeFile(file));
    }

    @PostMapping("/uploadByThirdFileUrls")
    @InnerAuth
    public Result<List<CommonFileVO>> uploadByThirdFileUrls(@RequestBody List<ThirdpartFileVO> files) {
        return Result.ok(fileUploadService.uploadByThirdFileUrls(files));
    }

    @PostMapping("/uploadByReportTable")
    @InnerAuth
    public Result<List<CommonFileVO>> uploadByReportTable(@RequestBody ThirdpartFileVO file) {
        return Result.ok(fileUploadService.uploadByReportTable(file));
    }
}
