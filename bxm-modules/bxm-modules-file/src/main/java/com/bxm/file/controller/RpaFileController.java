package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.file.bean.dto.rpa.RpaCheckResult;
import com.bxm.file.service.RpaFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/rpaFile")
@Api(tags = "rpa文件解析")
@Slf4j
public class RpaFileController {

    @Autowired
    private RpaFileService rpaFileService;

    @PostMapping("/createCheck")
    @ApiOperation("创建rpa数据校验，返回批次号，权限字符：file:rpaFile:createCheck")
    @RequiresPermissions("file:rpaFile:createCheck")
    public Result<String> createCheck(@RequestParam("rpaType") @ApiParam("交付类型，1-医社保，2-个税（工资薪金），3-国税，4-预认证，5-收入，6-个税（经营所得）") Integer rpaType,
                                            @RequestParam("operType") @ApiParam("操作类型，1-申报，2-扣款，3-其他，4-检查") Integer operType,
                                            @RequestParam("excelFile") @ApiParam("excl文件") MultipartFile excelFile,
                                            @RequestParam(value = "zipFile", required = false) @ApiParam("附件文件") MultipartFile zipFile,
                                            @RequestParam("period") @ApiParam("账期") Integer period,
                                            @RequestParam("queryDeptId") @ApiParam("数据申请部门id") Long queryDeptId,
                                            @RequestHeader("deptId") Long deptId) throws Exception {
        return Result.ok(rpaFileService.createCheck(rpaType, operType, excelFile, zipFile, period, queryDeptId, deptId));
    }

    @GetMapping("/progress/{batchNo}")
    @ApiOperation("查询校验进度")
    public Result<RpaCheckResult> getProgress(@PathVariable String batchNo) {
        return Result.ok(rpaFileService.getProgress(batchNo));
    }

    @PostMapping("/confirm/{batchNo}")
    @ApiOperation("确认数据")
    public Result confirmData(@PathVariable @ApiParam("批次号") String batchNo) {
        rpaFileService.confirmData(batchNo);
        return Result.ok();
    }

    @PostMapping("/downloadErrorFile")
    @ApiOperation("下载异常文件")
    public void downloadErrorFile(@RequestParam("batchNo") @ApiParam("批次号") String batchNo, HttpServletResponse response) throws IOException {
        try {
            rpaFileService.downloadErrorFile(batchNo, response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServerException("下载失败");
        }
    }

//    @RequiresPermissions("customer:deliver:downloadRpaRecordFile")
    @PostMapping("/downloadRpaRecordFile")
    @ApiOperation(value = "rpa上传记录列表下载文件，权限字符：customer:deliver:downloadRpaOriginFile")
    public void downloadRpaRecordFile(@RequestParam("rpaRecordId") @ApiParam("rpa上传记录id") Long rpaRecordId, HttpServletResponse response) {
        try {
            rpaFileService.downloadRpaRecordFile(rpaRecordId, response);
        } catch (Exception e) {
            log.error("下载失败:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/downloadRpaRecordAttachment")
    @ApiOperation(value = "rpa上传记录列表下载附件包")
    public void downloadRpaRecordAttachment(@RequestParam("rpaRecordId") @ApiParam("rpa上传记录id") Long rpaRecordId, HttpServletResponse response) {
        try {
            rpaFileService.downloadRpaRecordAttachment(rpaRecordId, response);
        } catch (Exception e) {
            log.error("下载失败:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

//    @RequiresPermissions("customer:deliver:downloadRpaDetailFile")
    @PostMapping("/downloadRpaDetailFile")
    @ApiOperation(value = "rpa明细列表导出数据，权限字符：customer:deliver:downloadRpaDetailFile")
    public void downloadRpaDetailFile(@RequestParam("rpaRecordId") @ApiParam("rpa上传记录id") Long rpaRecordId,
                                      @RequestParam(value = "customerName", required = false) @ApiParam("客户名") String customerName,
                                      @RequestParam(value = "status", required = false) @ApiParam("状态，0-待确认，1-处理中，2-成功，3-失败") Integer status,
                                      HttpServletResponse response) {
        try {
            rpaFileService.downloadRpaDetailFile(rpaRecordId, customerName, status, response);
        } catch (Exception e) {
            log.error("下载失败:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }
}
