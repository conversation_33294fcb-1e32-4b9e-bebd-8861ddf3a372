package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.MaterialDeliverNormalInAccountCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MaterialDeliverNormalInAccountCreateParser implements ExcelV2Parser<MaterialDeliverNormalInAccountCreateData> {

    @Override
    public List<MaterialDeliverNormalInAccountCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, MaterialDeliverNormalInAccountCreateData.class);
    }
}
