package com.bxm.file.parser;

import com.bxm.file.bean.dto.OperatingIncomePersonTaxReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class OperatingIncomePersonTaxInsuranceReportExceptionParser implements ExcelParser<OperatingIncomePersonTaxReportExceptionData>{
    @Override
    public List<OperatingIncomePersonTaxReportExceptionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析个税（经营所得）的 Excel 文件
        return ExcelUtils.parseExcelFile(file, OperatingIncomePersonTaxReportExceptionData.class);
    }
}
