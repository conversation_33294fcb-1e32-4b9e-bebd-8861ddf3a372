package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeGetProfitData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeGetProfitParser implements ExcelV2Parser<AccountingCashierIncomeGetProfitData> {

    @Override
    public List<AccountingCashierIncomeGetProfitData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeGetProfitData.class);
    }
}
