package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeUpdateProfitData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeUpdateProfitParser implements ExcelV2Parser<AccountingCashierIncomeUpdateProfitData> {

    @Override
    public List<AccountingCashierIncomeUpdateProfitData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeUpdateProfitData.class);
    }
}
