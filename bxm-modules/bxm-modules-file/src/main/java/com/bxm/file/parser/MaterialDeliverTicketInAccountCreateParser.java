package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.MaterialDeliverTicketInAccountCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MaterialDeliverTicketInAccountCreateParser implements ExcelV2Parser<MaterialDeliverTicketInAccountCreateData> {

    @Override
    public List<MaterialDeliverTicketInAccountCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, MaterialDeliverTicketInAccountCreateData.class);
    }
}
