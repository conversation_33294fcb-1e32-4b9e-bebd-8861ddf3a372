package com.bxm.file.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.file.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class OssService {

    private static final String ACCESS_KEY = "LTAI5t9TQPHCxG8vU9XXjfWa";

    private static final String SECRET_KEY = "******************************";

    private static final String ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";

    private static final String INNER_ENDPOINT = "oss-cn-hangzhou-internal.aliyuncs.com";

    private static final String BUCKET_NAME = "bxm410";

    private OSS ossClient;

    public OssService() {
        this.ossClient = new OSSClientBuilder().build(INNER_ENDPOINT, ACCESS_KEY, SECRET_KEY);
    }

    public void downloadFilesAndGenerateZip(List<String> fileKeys, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName, HttpServletResponse response) throws IOException {
        File zipFile = File.createTempFile("temp", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 下载所有文件并添加到zip
            for (String fileKey : fileKeys) {
                String[] fileKeyParts = fileKey.split("/");
                String baseDir = fileKeyParts[fileKeyParts.length - 2];
                addFileToZip(fileKey, baseDir, zos);
            }

            if (!ObjectUtils.isEmpty(dataMap)) {
                // 生成并添加Excel文件到zip
                Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                ZipEntry excelEntry = new ZipEntry(fileName + ".xlsx");
                zos.putNextEntry(excelEntry);
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    workbook.write(baos);
                    zos.write(baos.toByteArray());
                }
            }
        }

        // 设置响应头并返回zip文件
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".zip");
        try (InputStream is = new FileInputStream(zipFile);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } finally {
            zipFile.delete();
        }
    }

    private void addFileToZip(String key, String baseDir, ZipOutputStream zos) throws IOException {
        InputStream is = ossClient.getObject(BUCKET_NAME, key).getObjectContent();
        String zipEntryName = key.substring(key.indexOf(baseDir));
        zos.putNextEntry(new ZipEntry(zipEntryName));
        byte[] buffer = new byte[1024];
        int len;
        while ((len = is.read(buffer)) != -1) {
            zos.write(buffer, 0, len);
        }
        is.close();
    }

    private void addFileToZip(String key, String baseDir, String fileName, ZipOutputStream zos) throws IOException {
        if (StringUtils.isEmpty(baseDir)) {
            zos.putNextEntry(new ZipEntry(fileName));
        } else {
            zos.putNextEntry(new ZipEntry(baseDir + "/" + fileName));
        }
        if (!StringUtils.isEmpty(key)) {
            InputStream is = ossClient.getObject(BUCKET_NAME, key).getObjectContent();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                zos.write(buffer, 0, len);
            }
            is.close();
        }
    }

    public void downloadFilesAndGenerateZiByFiles(List<CommonFileVO> files, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName, HttpServletResponse response) throws IOException {
        File zipFile = File.createTempFile("temp", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 下载所有文件并添加到zip
            for (CommonFileVO file : files) {
                try {
                    String baseDir = file.getBaseDir();
                    addFileToZip(file.getFileUrl(), baseDir, file.getFileName(), zos);
                } catch (Exception e) {
                    log.error("文件下载失败，file:{}", JSONObject.toJSONString(file));
                }
            }

            // 生成并添加Excel文件到zip
            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
            ZipEntry excelEntry = new ZipEntry(fileName + ".xlsx");
            zos.putNextEntry(excelEntry);
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                workbook.write(baos);
                zos.write(baos.toByteArray());
            }
        }

        // 设置响应头并返回zip文件
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=originFile.zip");
        try (InputStream is = new FileInputStream(zipFile);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } finally {
            zipFile.delete();
        }
    }

    public String downloadFilesAndGenerateZiByFilesAndUpload(List<CommonFileVO> files, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName) throws IOException {
        File zipFile = File.createTempFile("temp", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 下载所有文件并添加到zip
            for (CommonFileVO file : files) {
                try {
                    String baseDir = file.getBaseDir();
                    addFileToZip(file.getFileUrl(), baseDir, file.getFileName(), zos);
                } catch (Exception e) {
                    log.error("文件下载失败，file:{}", JSONObject.toJSONString(file));
                }
            }

            // 生成并添加Excel文件到zip
            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
            ZipEntry excelEntry = new ZipEntry(fileName + ".xlsx");
            zos.putNextEntry(excelEntry);
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                workbook.write(baos);
                zos.write(baos.toByteArray());
            }
        }

        // 上传到OSS
        String ossUrl = uploadToOSS(zipFile);

        // 上传成功后，删除临时文件
        if (zipFile.exists()) {
            zipFile.delete();
        }

        return ossUrl;
    }

    public void downloadFilesAndGenerateZiByFiles(List<CommonFileVO> files, HttpServletResponse response) throws IOException {
        File zipFile = File.createTempFile("temp", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 下载所有文件并添加到zip
            for (CommonFileVO file : files) {
                try {
                    String baseDir = file.getBaseDir();
                    addFileToZip(file.getFileUrl(), baseDir, file.getFileName(), zos);
                } catch (Exception e) {
//                    log.error("文件下载失败，file:{}", JSONObject.toJSONString(file));
                }
            }
        }

        // 设置响应头并返回zip文件
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=originFile.zip");
        try (InputStream is = new FileInputStream(zipFile);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } finally {
            zipFile.delete();
        }
    }

    public String uploadToOSS(File file) {
        try {
            // 上传文件
            String objectName = "export/" + file.getName(); // 根据需求生成文件路径
            ossClient.putObject(BUCKET_NAME, objectName, new FileInputStream(file));

            // 返回文件的OSS URL
            return objectName;
        } catch (Exception e) {
//            e.printStackTrace();
            return null;
        }
    }
}
