package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeRpaUpdateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeRpaUpdateParser implements ExcelV2Parser<AccountingCashierIncomeRpaUpdateData> {

    @Override
    public List<AccountingCashierIncomeRpaUpdateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeRpaUpdateData.class);
    }
}
