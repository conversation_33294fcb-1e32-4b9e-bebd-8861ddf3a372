package com.bxm.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.file.domain.DownloadRecord;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IDownloadRecordService extends IService<DownloadRecord>
{
    void updateFailWhenDestroy();

    Long createRecord(String title, Long dataCount, Long attachmentCount, Object obj, DownloadType downloadType);

    Boolean checkHasDoingRecord(Long userId);

    void updateDataCount(Long recordId, Long dataCount);

    void updateDownloadError(Long recordId, String errorMsg);
}
