package com.bxm.file.service;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.file.domain.DownloadRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class AsyncService {

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private OssService ossService;

    @Async
    public void uploadExport(ExcelUtil util, List list, String title, Long downloadRecordId) {
        if (Objects.isNull(downloadRecordId)) {
            return;
        }
        DownloadRecord downloadRecord = downloadRecordService.getById(downloadRecordId);
        if (Objects.isNull(downloadRecord)) {
            return;
        }
        try {
            String downloadUrl = util.exportExcelAndUpload(list, title);
            downloadRecord.setDownloadUrl(downloadUrl);
            downloadRecord.setStatus(1);
            downloadRecord.setDataCount((long) list.size());
            downloadRecordService.updateById(downloadRecord);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("recordId:{}, 导出上传失败，错误原因:{}", downloadRecordId, errorMsg);
            downloadRecord.setStatus(2);
            downloadRecord.setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg);
            downloadRecordService.updateById(downloadRecord);
        }
    }

    @Async
    public void uploadExport(List<CommonFileVO> files, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName, Long downloadRecordId) {
        if (Objects.isNull(downloadRecordId)) {
            return;
        }
        DownloadRecord downloadRecord = downloadRecordService.getById(downloadRecordId);
        if (Objects.isNull(downloadRecord)) {
            return;
        }
        try {
            String downloadUrl = ossService.downloadFilesAndGenerateZiByFilesAndUpload(files, dataMap, sheetClassMap, fileName);
            downloadRecord.setDownloadUrl(downloadUrl);
            downloadRecord.setStatus(1);
//            downloadRecord.setDataCount((long) dataMap.values().size());
            downloadRecordService.updateById(downloadRecord);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("recordId:{}, 导出上传失败，错误原因:{}", downloadRecordId, errorMsg);
            downloadRecord.setStatus(2);
            downloadRecord.setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg);
            downloadRecordService.updateById(downloadRecord);
        }
    }
}
