package com.bxm.file.parser;

import com.bxm.file.bean.dto.PersonTaxDeductionExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class PersonTaxInsuranceDeductionExceptionParser implements ExcelParser<PersonTaxDeductionExceptionData>{
    @Override
    public List<PersonTaxDeductionExceptionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析个税（工资薪金）的 Excel 文件
        return ExcelUtils.parseExcelFile(file, PersonTaxDeductionExceptionData.class);
    }
}
