package com.bxm.file.parser;

import com.bxm.file.bean.dto.OperatingIncomePersonTaxReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class OperatingIncomePersonTaxInsuranceReportParser implements ExcelParser<OperatingIncomePersonTaxReportData>{
    @Override
    public List<OperatingIncomePersonTaxReportData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析个税（经营所得）的 Excel 文件
        return ExcelUtils.parseExcelFile(file, OperatingIncomePersonTaxReportData.class);
    }
}
