package com.bxm.file.parser;

import com.bxm.file.bean.dto.MedicalInsuranceReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MedicalInsuranceReportExceptionParser implements ExcelParser<MedicalInsuranceReportExceptionData>{
    @Override
    public List<MedicalInsuranceReportExceptionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析医疗保险的 Excel 文件
        return ExcelUtils.parseExcelFile(file, MedicalInsuranceReportExceptionData.class);
    }
}
