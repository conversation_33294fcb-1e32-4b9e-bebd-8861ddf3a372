package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.TimesReportSupplementReportFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class TimesReportSupplementReportFileParser implements ExcelV2Parser<TimesReportSupplementReportFileData> {

    @Override
    public List<TimesReportSupplementReportFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, TimesReportSupplementReportFileData.class);
    }
}
