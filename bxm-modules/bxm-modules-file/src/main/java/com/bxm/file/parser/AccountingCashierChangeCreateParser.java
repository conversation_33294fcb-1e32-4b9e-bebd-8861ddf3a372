package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierChangeCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierChangeCreateParser implements ExcelV2Parser<AccountingCashierChangeCreateData> {

    @Override
    public List<AccountingCashierChangeCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierChangeCreateData.class);
    }
}
