package com.bxm.customer.service;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.OpenApiNoticeRecord;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.mapper.OpenApiNoticeRecordMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StatisticService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    private OpenApiNoticeRecordMapper openApiNoticeRecordMapper;

    // 个税类型的notice_function
    private static final List<String> PERSON_TAX_NOTICE_FUNCTION = Lists.newArrayList("个税扣款", "个税扣款状态查询", "个税检查", "个税检查状态查询", "个税申报", "个税申报状态查询", "个税申报表下载");
    // 国税类型的notice_function
    private static final List<String> COUNTRY_TAX_NOTICE_FUNCTION = Lists.newArrayList("openapiCommonDeduction_prod", "openapiCommonNotice_prod", "openapiCommonSupplement_prod", "openapiSupplement_prod", "openapiReport_prod");
    // 社保类型的notice_function
    private static final List<String> SOCIAL_NOTICE_FUNCTION = Lists.newArrayList("openapiCommonReport_prod");
    // 银行流水类型的notice_function
    private static final List<String> BANK_NOTICE_FUNCTION = Lists.newArrayList("凭证生成", "查询已申报已扣款列表", "查询电子材料入账结果", "查询银企入账结果", "电子材料导入通知", "银行流水银企下载通知");
    // 系统同步类型的notice_function
    private static final List<String> SYSTEM_SYNC_NOTICE_FUNCTION = Lists.newArrayList("查询企业信息", "通知医社保", "客户代理状态设置");
    // 发票更新类型的notice_function
    private static final List<String> INVOICE_UPDATE_NOTICE_FUNCTION = Lists.newArrayList("发票更新");

    public DeliverAtfStatisticDTO getDeliverAtfStatistic() {
        int deliverStatisticRate;
        String deliverStatisticRateValue = redisService.getCacheObject(CacheConstants.DELIVER_STATISTIC_RATE);
        if (StringUtils.isEmpty(deliverStatisticRateValue)) {
            deliverStatisticRate = 1;
        } else {
            deliverStatisticRate = Integer.parseInt(deliverStatisticRateValue);
        }
        String today = LocalDate.now().toString();
        Integer deliverOperLogRealCount = redisService.getCacheObject(String.format(CacheConstants.BUSINESS_LOT_COUNT_DAILY, today, BusinessLogBusinessType.CUSTOMER_SERVICE_DELIVER.getCode()));
        deliverOperLogRealCount = null == deliverOperLogRealCount ? 0 : deliverOperLogRealCount;
        Integer deliverOperLogOtherRealCount = redisService.getCacheObject(String.format(CacheConstants.BUSINESS_LOT_COUNT_DAILY_OTHER_TYPE, today, BusinessLogBusinessType.CUSTOMER_SERVICE_DELIVER.getCode()));
        deliverOperLogOtherRealCount = null == deliverOperLogOtherRealCount ? 0 : deliverOperLogOtherRealCount;
        return DeliverAtfStatisticDTO.builder()
                .deliverAtfPercent(getPercent((long) deliverOperLogOtherRealCount, (long) deliverOperLogRealCount, 4))
                .rate(deliverStatisticRate)
                .deliverOperLogOtherRealCount((long) deliverOperLogOtherRealCount)
                .deliverOperLogRealCount((long) deliverOperLogRealCount)
                .deliverOperLogShowCount((long) deliverOperLogRealCount * deliverStatisticRate)
                .build();
    }

    public DeliverProgressStatisticDTO getDeliverProgressStatistic() {
        Integer period = DateUtils.getNowPeriod();
        Integer prePeriod = DateUtils.getPrePeriod();
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .select(CustomerServicePeriodMonth::getMedicalDeliverShouldReportStatus, CustomerServicePeriodMonth::getMedicalDeliverStatus,
                        CustomerServicePeriodMonth::getSocialDeliverShouldReportStatus, CustomerServicePeriodMonth::getSocialDeliverStatus));
        List<CustomerServicePeriodMonth> preCustomerServicePeriodMonths = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, prePeriod)
                .select(CustomerServicePeriodMonth::getPersonTaxDeliverShouldReportStatus, CustomerServicePeriodMonth::getPersonTaxDeliverStatus,
                        CustomerServicePeriodMonth::getNationalTaxDeliverShouldReportStatus, CustomerServicePeriodMonth::getNationalTaxDeliverStatus));
        return DeliverProgressStatisticDTO.builder()
                .medicalSocialTaxProgress(getMedicalSocialTaxProgress(customerServicePeriodMonths))
                .nationalTaxProgress(getNationalTaxProgress(preCustomerServicePeriodMonths))
                .personTaxProgress(getPersonTaxProgress(preCustomerServicePeriodMonths))
                .build();
    }

    private DeliverTypeProgressDTO getPersonTaxProgress(List<CustomerServicePeriodMonth> customerServicePeriodMonths) {
        Long totalCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getPersonTaxDeliverShouldReportStatus(), -1)).count();
        Long waitSubmitCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getPersonTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(-1, 6, 11).contains(r.getPersonTaxDeliverStatus())).count();
        Long waitDeliverCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getPersonTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(0, 1, 3, 4).contains(r.getPersonTaxDeliverStatus())).count();
        Long waitConfirmCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getPersonTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(2, 7, 9).contains(r.getPersonTaxDeliverStatus())).count();
        Long completedCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getPersonTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(5, 8, 10).contains(r.getPersonTaxDeliverStatus())).count();
        return DeliverTypeProgressDTO.builder()
                .totalCount(totalCount)
                .waitSubmitCount(waitSubmitCount)
                .waitDeliverCount(waitDeliverCount)
                .waitConfirmCount(waitConfirmCount)
                .completedCount(completedCount)
                .waitSubmitPercent(getPercent(waitSubmitCount, totalCount, 4))
                .waitDeliverPercent(getPercent(waitDeliverCount, totalCount, 4))
                .waitConfirmPercent(getPercent(waitConfirmCount, totalCount, 4))
                .completedPercent(getPercent(completedCount, totalCount, 4))
                .build();
    }

    private DeliverTypeProgressDTO getNationalTaxProgress(List<CustomerServicePeriodMonth> customerServicePeriodMonths) {
        Long totalCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getNationalTaxDeliverShouldReportStatus(), -1)).count();
        Long waitSubmitCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getNationalTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(-1, 6, 11).contains(r.getNationalTaxDeliverStatus())).count();
        Long waitDeliverCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getNationalTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(0, 1, 3, 4).contains(r.getNationalTaxDeliverStatus())).count();
        Long waitConfirmCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getNationalTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(2, 7, 9).contains(r.getNationalTaxDeliverStatus())).count();
        Long completedCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getNationalTaxDeliverShouldReportStatus(), -1) && Lists.newArrayList(5, 8, 10).contains(r.getNationalTaxDeliverStatus())).count();
        return DeliverTypeProgressDTO.builder()
                .totalCount(totalCount)
                .waitSubmitCount(waitSubmitCount)
                .waitDeliverCount(waitDeliverCount)
                .waitConfirmCount(waitConfirmCount)
                .completedCount(completedCount)
                .waitSubmitPercent(getPercent(waitSubmitCount, totalCount, 4))
                .waitDeliverPercent(getPercent(waitDeliverCount, totalCount, 4))
                .waitConfirmPercent(getPercent(waitConfirmCount, totalCount, 4))
                .completedPercent(getPercent(completedCount, totalCount, 4))
                .build();
    }

    private DeliverTypeProgressDTO getMedicalSocialTaxProgress(List<CustomerServicePeriodMonth> customerServicePeriodMonths) {
        Long totalCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getMedicalDeliverShouldReportStatus(), -1) || Objects.equals(r.getSocialDeliverShouldReportStatus(), -1)).count();
        Long waitSubmitCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> (Objects.equals(r.getMedicalDeliverShouldReportStatus(), -1) || Objects.equals(r.getSocialDeliverShouldReportStatus(), -1)) && (Lists.newArrayList(-1, 6, 11).contains(r.getMedicalDeliverStatus()) || Lists.newArrayList(-1, 6, 11).contains(r.getSocialDeliverStatus()))).count();
        Long waitDeliverCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> (Objects.equals(r.getMedicalDeliverShouldReportStatus(), -1) || Objects.equals(r.getSocialDeliverShouldReportStatus(), -1)) && ((Lists.newArrayList(0, 1, 3, 4).contains(r.getMedicalDeliverStatus()) && Lists.newArrayList(2, 7, 9, 5, 8, 10, -2).contains(r.getSocialDeliverStatus())) || (Lists.newArrayList(0, 1, 3, 4).contains(r.getSocialDeliverStatus()) && Lists.newArrayList(2, 7, 9, 5, 8, 10, -2).contains(r.getMedicalDeliverStatus())))).count();
        Long waitConfirmCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> (Objects.equals(r.getMedicalDeliverShouldReportStatus(), -1) || Objects.equals(r.getSocialDeliverShouldReportStatus(), -1)) && ((Lists.newArrayList(2, 7, 9).contains(r.getMedicalDeliverStatus()) && Lists.newArrayList(2, 7, 9, 5, 8, 10, -2).contains(r.getSocialDeliverStatus())) || (Lists.newArrayList(2, 7, 9).contains(r.getSocialDeliverStatus()) && Lists.newArrayList(2, 7, 9, 5, 8, 10, -2).contains(r.getMedicalDeliverStatus())))).count();
        Long completedCount = ObjectUtils.isEmpty(customerServicePeriodMonths) ? 0L :
                customerServicePeriodMonths.stream().filter(r -> (Objects.equals(r.getMedicalDeliverShouldReportStatus(), -1) || Objects.equals(r.getSocialDeliverShouldReportStatus(), -1)) && Lists.newArrayList(5, 8, 10, -2).contains(r.getMedicalDeliverStatus()) && Lists.newArrayList(5, 8, 10, -2).contains(r.getSocialDeliverStatus())).count();
        return DeliverTypeProgressDTO.builder()
                .totalCount(totalCount)
                .waitSubmitCount(waitSubmitCount)
                .waitDeliverCount(waitDeliverCount)
                .waitConfirmCount(waitConfirmCount)
                .completedCount(completedCount)
                .waitSubmitPercent(getPercent(waitSubmitCount, totalCount, 4))
                .waitDeliverPercent(getPercent(waitDeliverCount, totalCount, 4))
                .waitConfirmPercent(getPercent(waitConfirmCount, totalCount, 4))
                .completedPercent(getPercent(completedCount, totalCount, 4))
                .build();
    }

    public AccountingCashierProgressStatisticDTO getAccountingCashierProgressStatistic() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD));
        String todayStart = today + " 00:00:00";
        String todayEnd = today + " 23:59:59";
        int accountingCashierStatisticRate;
        String accountingCashierStatisticRateValue = redisService.getCacheObject(CacheConstants.ACCOUNTING_CASHIER_STATISTIC_RATE);
        if (StringUtils.isEmpty(accountingCashierStatisticRateValue)) {
            accountingCashierStatisticRate = 1;
        } else {
            accountingCashierStatisticRate = Integer.parseInt(accountingCashierStatisticRateValue);
        }
        int bankCompleteRealCount = customerServiceCashierAccountingMapper.selectCount(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())
                .ge(CustomerServiceCashierAccounting::getCompleteTime, todayStart).le(CustomerServiceCashierAccounting::getCompleteTime, todayEnd));
        int inAccountCompleteRealCount = customerServiceCashierAccountingMapper.selectCount(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .ge(CustomerServiceCashierAccounting::getCompleteTime, todayStart).le(CustomerServiceCashierAccounting::getCompleteTime, todayEnd));
        int inAccountEndRealCount = customerServiceCashierAccountingMapper.selectCount(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .eq(CustomerServiceCashierAccounting::getEndTime, today));
        int newCreateRealCount = customerServiceCashierAccountingMapper.selectCount(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .ge(CustomerServiceCashierAccounting::getCreateTime, todayStart).le(CustomerServiceCashierAccounting::getCreateTime, todayEnd));
        return AccountingCashierProgressStatisticDTO.builder()
                .rate(accountingCashierStatisticRate)
                .bankCompleteRealCountDaily((long) bankCompleteRealCount)
                .bankCompleteShowCountDaily((long) accountingCashierStatisticRate * bankCompleteRealCount)
                .inAccountCompleteRealCountDaily((long) inAccountCompleteRealCount)
                .inAccountCompleteShowCountDaily((long) accountingCashierStatisticRate * inAccountCompleteRealCount)
                .inAccountEndRealCountDaily((long) inAccountEndRealCount)
                .inAccountEndShowCountDaily((long) accountingCashierStatisticRate * inAccountEndRealCount)
                .newCreateRealCountDaily((long) newCreateRealCount)
                .newCreateShowCountDaily((long) accountingCashierStatisticRate * newCreateRealCount)
                .build();
    }

    public AccountingCashierAtfStatisticDTO getAccountingCashierAtfStatistic() {
        String today = LocalDate.now().toString();
        Integer accountingCashierOperLogRealCount = redisService.getCacheObject(String.format(CacheConstants.BUSINESS_LOT_COUNT_DAILY, today, BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode()));
        accountingCashierOperLogRealCount = null == accountingCashierOperLogRealCount ? 0 : accountingCashierOperLogRealCount;
        Integer accountingCashierOperLogOtherRealCount = redisService.getCacheObject(String.format(CacheConstants.BUSINESS_LOT_COUNT_DAILY_OTHER_TYPE, today, BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode()));
        accountingCashierOperLogOtherRealCount = null == accountingCashierOperLogOtherRealCount ? 0 : accountingCashierOperLogOtherRealCount;
        return AccountingCashierAtfStatisticDTO.builder()
                .accountingCashierOperLogRealCount((long) accountingCashierOperLogRealCount)
                .accountingCashierOperLogOtherRealCount((long) accountingCashierOperLogOtherRealCount)
                .accountingCashierAtfPercent(getPercent((long) accountingCashierOperLogOtherRealCount, (long) accountingCashierOperLogRealCount, 4))
                .build();
    }

    public Map<String, AccountingCashierMonthProgressDTO> getAccountingCashierMonthProgressStatistic() {
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        if (month == 1) {
            year = year - 1;
        }
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getYear, year)
                .select(CustomerServicePeriodMonth::getServiceType, CustomerServicePeriodMonth::getPeriod, CustomerServicePeriodMonth::getBankPaymentResult, CustomerServicePeriodMonth::getInAccountStatus, CustomerServicePeriodMonth::getSettleAccountStatus));
        customerServicePeriodMonths = ObjectUtils.isEmpty(customerServicePeriodMonths) ? Lists.newArrayList() : customerServicePeriodMonths.stream().filter(r -> Objects.equals(r.getServiceType(), 1)).collect(Collectors.toList());
        Map<Integer, List<CustomerServicePeriodMonth>> periodMap = customerServicePeriodMonths.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getPeriod));
        int start = year * 100 + 1;
        int end = DateUtils.getPrePeriod();
        Map<String, AccountingCashierMonthProgressDTO> result = new HashMap<>();
        while (start <= end) {
            List<CustomerServicePeriodMonth> periodList = periodMap.get(start);
            Long total = ObjectUtils.isEmpty(periodList) ? 0L : (long) periodList.size();
            Long bankCompleteCount = ObjectUtils.isEmpty(periodList) ? 0L : periodList.stream().filter(period -> Lists.newArrayList(0, 3, 5, 6, 7, 8).contains(period.getBankPaymentResult())).count();
            Long inAccountCompleteCount = ObjectUtils.isEmpty(periodList) ? 0L : periodList.stream().filter(period -> Lists.newArrayList(2, 3).contains(period.getInAccountStatus())).count();
            Long inAccountEndCount = ObjectUtils.isEmpty(periodList) ? 0L : periodList.stream().filter(period -> Objects.equals(3, period.getSettleAccountStatus())).count();
            result.put(start % 100 + "月", AccountingCashierMonthProgressDTO.builder()
                            .bankCompleteRate(getPercent(bankCompleteCount, total, 4))
                            .inAccountCompleteRate(getPercent(inAccountCompleteCount, total, 4))
                            .inAccountEndRate(getPercent(inAccountEndCount, total, 4))
                    .build());
            start++;
        }
        return result;
    }

    public AtfStatisticDTO getAtfStatistic() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD));
        String todayStart = today + " 00:00:00";
        String todayEnd = today + " 23:59:59";
        List<OpenApiNoticeRecord> noticeRecords = openApiNoticeRecordMapper.selectList(new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .eq(OpenApiNoticeRecord::getIsDel, false)
                .ge(OpenApiNoticeRecord::getCreateTime, todayStart).le(OpenApiNoticeRecord::getCreateTime, todayEnd)
                .select(OpenApiNoticeRecord::getCreateTime, OpenApiNoticeRecord::getUpdateTime, OpenApiNoticeRecord::getNoticeFunction));
        List<AtfTypeStatisticDTO> atfTypeStatisticList = Lists.newArrayList();
        long totalSeconds = 0L; long personTaxTotalSeconds = 0L; long nationalTaxTotalSeconds = 0L; long socialTaxTotalSeconds = 0L; long bankFlowTotalSeconds = 0L; long systemTotalSeconds = 0L; long invoiceTotalSeconds = 0L;
        long personTaxTotalCount = 0L; long nationalTaxTotalCount = 0L; long socialTaxTotalCount = 0L; long bankFlowTotalCount = 0L; long systemTotalCount = 0L; long invoiceTotalCount = 0L;
        for (OpenApiNoticeRecord noticeRecord : noticeRecords) {
            long useTime = DateUtils.localDateToSecond(noticeRecord.getUpdateTime()) - DateUtils.localDateToSecond(noticeRecord.getCreateTime());
            totalSeconds += useTime;
            if (PERSON_TAX_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                personTaxTotalSeconds += useTime;
                personTaxTotalCount++;
            } else if (COUNTRY_TAX_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                nationalTaxTotalSeconds += useTime;
                nationalTaxTotalCount++;
            } else if (SOCIAL_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                socialTaxTotalSeconds += useTime;
                socialTaxTotalCount++;
            } else if (BANK_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                bankFlowTotalSeconds += useTime;
                bankFlowTotalCount++;
            } else if (SYSTEM_SYNC_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                systemTotalSeconds += useTime;
                systemTotalCount++;
            } else if (INVOICE_UPDATE_NOTICE_FUNCTION.contains(noticeRecord.getNoticeFunction())) {
                invoiceTotalSeconds += useTime;
                invoiceTotalCount++;
            }
        }
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("个税").taskCount(personTaxTotalCount).averageSecond(personTaxTotalCount == 0L ? null : personTaxTotalSeconds / personTaxTotalCount).build());
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("国税").taskCount(nationalTaxTotalCount).averageSecond(nationalTaxTotalCount == 0L ? null : nationalTaxTotalSeconds / nationalTaxTotalCount).build());
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("社保").taskCount(socialTaxTotalCount).averageSecond(socialTaxTotalCount == 0L ? null : socialTaxTotalSeconds / socialTaxTotalCount).build());
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("银行流水").taskCount(bankFlowTotalCount).averageSecond(bankFlowTotalCount == 0L ? null : bankFlowTotalSeconds / bankFlowTotalCount).build());
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("系统同步").taskCount(systemTotalCount).averageSecond(systemTotalCount == 0L ? null : systemTotalSeconds / systemTotalCount).build());
        atfTypeStatisticList.add(AtfTypeStatisticDTO.builder().atfType("发票").taskCount(invoiceTotalCount).averageSecond(invoiceTotalCount == 0L ? null : invoiceTotalSeconds / invoiceTotalCount).build());
        return AtfStatisticDTO.builder()
                .taskCount((long) noticeRecords.size())
                .averageSecond(ObjectUtils.isEmpty(noticeRecords) ? null : totalSeconds / noticeRecords.size())
                .atfTypeStatisticList(atfTypeStatisticList)
                .build();
    }

    private static BigDecimal getPercent(Long one, Long two, int scale) {
        if (two <= 0L) {
            return null;
        }
        String percent = NumberUtil.formatPercent(new BigDecimal(one.toString()).divide(new BigDecimal(two.toString()), scale, RoundingMode.HALF_UP).doubleValue(), 2);
        return new BigDecimal(percent.substring(0, percent.length() - 1));
    }
}
