package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已扣款状态变更策略
 *
 * 处理从"已扣款"状态到其他状态的转换变更
 *
 * 注意：已扣款是终态状态，一般情况下不允许转换到其他状态
 * 仅在特殊情况下允许转换：
 * 1. DEDUCTION_COMPLETED -> DEDUCTION_EXCEPTION (发现扣款异常，需要处理)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionCompletedChangeStrategy implements StatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            return false;
        }

        // 已扣款状态是终态，仅在特殊情况下允许转换到扣款异常状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Processing status change from DEDUCTION_COMPLETED to {} for order: {}",
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            // 验证转换到扣款异常状态
            validateToDeductionException(order, request);
        } else {
            throw new IllegalArgumentException("已扣款状态是终态，不支持转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    /**
     * 验证转换到扣款异常状态
     *
     * 这种转换通常发生在以下情况：
     * 1. 发现扣款金额错误
     * 2. 客户投诉扣款问题
     * 3. 系统扣款异常需要人工处理
     */
    private void validateToDeductionException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（需要高级权限，如财务主管或系统管理员）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证异常原因必须详细说明
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("从已扣款状态转换到异常状态必须提供详细原因");
        }

        // 异常原因长度验证（要求更详细的说明）
        if (request.getReason().length() < 20) {
            throw new IllegalArgumentException("异常原因描述不能少于20个字符，需要详细说明问题");
        }

        // 验证备注信息（用于记录处理过程）
        if (request.getRemark() == null || request.getRemark().trim().isEmpty()) {
            throw new IllegalArgumentException("从终态转换必须提供备注信息");
        }

        log.warn("Converting from DEDUCTION_COMPLETED to DEDUCTION_EXCEPTION for order: {}, reason: {}",
                request.getDeliveryOrderNo(), request.getReason());
    }
}
