package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已确认待扣款状态变更策略
 *
 * 处理从"已确认待扣款"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_COMPLETED (扣款正常完成)
 * 2. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_EXCEPTION (扣款异常)
 * 3. CONFIRMED_PENDING_DEDUCTION -> PENDING_CONFIRMATION (退回待确认)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class ConfirmedPendingDeductionChangeStrategy implements StatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Processing status change from CONFIRMED_PENDING_DEDUCTION to {} for order: {}",
                request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            // 验证扣款完成
            validateDeductionCompleted(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            // 验证扣款异常
            validateDeductionException(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证退回待确认
            validateReturnToConfirmation(order, request);
        } else {
            throw new IllegalArgumentException("不支持从已确认待扣款状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }

    /**
     * 验证扣款完成
     */
    private void validateDeductionCompleted(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（财务或系统权限）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证扣款相关信息
        if (order.getSyncHandlingFee() == null) {
            throw new IllegalArgumentException("是否同步手续费标志不能为空");
        }

        // 验证客户信息完整性
        if (order.getCustomerId() == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }

        if (order.getCreditCode() == null || order.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("统一社会信用代码不能为空");
        }

        // 验证纳税人类型
        if (order.getTaxpayerType() == null || (order.getTaxpayerType() != 1 && order.getTaxpayerType() != 2)) {
            throw new IllegalArgumentException("纳税人类型必须为1（小规模）或2（一般纳税人）");
        }

        log.info("Deduction completion validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证扣款异常
     */
    private void validateDeductionException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证异常原因必须提供
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("扣款异常必须提供异常原因");
        }

        // 异常原因长度验证
        if (request.getReason().length() < 10) {
            throw new IllegalArgumentException("扣款异常原因描述不能少于10个字符");
        }

        log.info("Deduction exception validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回待确认状态
     */
    private void validateReturnToConfirmation(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限（财务权限）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证退回原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("退回待确认状态必须提供原因");
        }

        log.info("Return to confirmation validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
