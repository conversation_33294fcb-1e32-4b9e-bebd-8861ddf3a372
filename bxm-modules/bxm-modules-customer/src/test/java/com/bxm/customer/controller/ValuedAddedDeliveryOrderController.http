### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的upsert方法
### 基于ValueAddedDeliveryOrderVO参数完善的真实测试场景
###
### 字段说明：
### - valueAddedItemTypeId: 增值事项类型 (1-4)
### - taxpayerType: 纳税性质 (1-小规模纳税人, 2-一般纳税人)
### - accountingInfo: 账务类型信息 (STANDARD-标准账务, NON_STANDARD-非标账务)
### - syncHandlingFee: 是否同步手续费 (true/false)
### - syncReassignment: 是否同步改派 (true/false)
### - modifyDueDate: 是否修改工期 (true/false)

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjkzZGI3NzE2LWNmNDgtNGZkNy04OTBiLTZiNjY1MGI2NjIxMSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.bY13WCg1xiw9TZXB12-VkKug7m9FoxcgFD35JR4_AXcVOMyygh4XAoqYMOYi_CSEXLbkXCC3FIGzXue1BbEt0A
### ========================================
### 1. 正常新增场景测试 - 小规模纳税人 + 标准账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "customerName": "测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName":"个税-工资",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "请按账期提供银行流水（对账单和回单）、进销票、补账期间的个税明细报表明细的三大报表、余额表、固定资产明细表、无形资产明细表、库存表支持jpg、png、pdf、word、xls",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 101,
  "businessTopDeptId": 100,
  "status": "DRAFT",
  "customerId": 1001
}

### ========================================
### 2. 正常新增场景测试 - 一般纳税人 + 非标账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430002C2D",
  "customerName": "一般纳税人测试公司",
  "creditCode": "91***************X",
  "taxNo": "***************",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "110000199002022345",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "社医保相关材料提交要求",
  "syncReassignment": true,
  "modifyDueDate": false,
  "ddl": "2025-06-30",
  "initiateDeptId": 2,
  "businessDeptId": 102,
  "businessTopDeptId": 100,
  "customerId": 1001
}

### ========================================
### 3. 正常更新场景测试 - 包含ID的更新操作
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "VAD2508051430003E3F",
  "customerName": "更新测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199003033456",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "SPECIAL_INDUSTRY"]
  },
  "requirements": "更新后的交付要求",
  "syncReassignment": false,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "initiateDeptId": 1,
  "businessDeptId": 103,
  "businessTopDeptId": 100,
  "status": "DELIVERY_COMPLETED"
}

### ========================================
### 4. AccountingInfoVO验证测试 - 标准账务包含子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430004G4H",
  "customerName": "账务类型验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingInfo": {
    "mainType": "STANDARD",
    "subTypes": ["HIGH_TECH"]
  }
}

### ========================================
### 5. AccountingInfoVO验证测试 - 非标账务缺少子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430005I5J",
  "customerName": "非标账务验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingInfo": {
    "mainType": "NON_STANDARD"
  }
}

### ========================================
### 6. 参数验证测试 - 客户名称为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430006K6L",
  "customerName": "",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 7. 参数验证测试 - 增值事项为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430007M7N",
  "customerName": "增值事项为空测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": null
}

### ========================================
### 8. 参数验证测试 - 增值事项字段缺失
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430008O8P",
  "customerName": "增值事项字段缺失测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1
}

### ========================================
### 9. 参数验证测试 - 信用代码格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430009Q9R",
  "customerName": "格式错误测试公司",
  "creditCode": "invalid_credit_code",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 10. 参数验证测试 - 纳税性质超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430010S0T",
  "customerName": "纳税性质错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 5,
  "valueAddedItemTypeId": 1
}

### ========================================
### 11. 参数验证测试 - 增值事项超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430011U1V",
  "customerName": "增值事项错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 10
}

### ========================================
### 12. 账期验证测试 - 开始时间晚于结束时间
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430012W2X",
  "customerName": "账期验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202512,
  "accountingPeriodEnd": 202501
}

### ========================================
### 13. 账期验证测试 - 账期格式错误（超出范围）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430013Y3Z",
  "customerName": "账期格式错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 199912,
  "accountingPeriodEnd": 300001
}

### ========================================
### 14. 布尔字段测试 - 所有布尔字段为true
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430014A4B",
  "customerName": "布尔字段测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "syncHandlingFee": true,
  "syncReassignment": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}

### ========================================
### 15. 边界条件测试 - 最小有效数据
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430015C5D",
  "customerName": "最小数据测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 16. 边界条件测试 - 最大长度字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430016E6F",
  "customerName": "这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "requirements": "这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制"
}

### ========================================
### 17. 完整业务场景测试 - 个税账号类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430017G7H",
  "customerName": "个税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199004044567",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "个税账号相关材料要求",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-03-31",
  "initiateDeptId": 3,
  "businessDeptId": 104,
  "businessTopDeptId": 100
}

### ========================================
### 18. 测试生成交付单编号接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Authorization: {{authorization}}

### ========================================
### 19. 测试根据交付单编号查询接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2508051430003E3F
Authorization: {{authorization}}

### ========================================
### 20. "改账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430020M0N",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "customId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "改账场景测试：需要根据客户服务ID更新相关字段信息",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 105,
  "businessTopDeptId": 100,
  "status": "DRAFT"
}

### ========================================
### 21. "改账"场景测试 - customId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430021O1P",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 22. "改账"场景测试 - customId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430022Q2R",
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "customId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 23. "补账"场景测试 - 正常情况
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430023S3T",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199002022345",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账场景测试：自动补全202501-202512期间缺失的账期记录",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 106,
  "businessTopDeptId": 100,
  "status": "DRAFT"
}

### ========================================
### 24. "补账"场景测试 - customerId为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430024U4V",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 25. "补账"场景测试 - 账期格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430025W5X",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202513,
  "accountingPeriodEnd": 202501,
  "contactMobile": "***********"
}

### ========================================
### 26. "补账"场景测试 - customerId不存在
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430026Y6Z",
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 99999,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 27. 边界值测试 - itemName为其他值（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430027A7B",
  "customerName": "普通增值服务测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "其他服务",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 28. 边界值测试 - itemName为空（不触发特殊校验）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430028C8D",
  "customerName": "无itemName测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********"
}

### ========================================
### 29. 综合场景测试 - 改账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430029E9F",
  "customerName": "改账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "itemName": "改账",
  "customId": 1,
  "customerId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199005055678",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH"]
  },
  "requirements": "改账综合测试：包含所有字段的完整测试",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "initiateDeptId": 1,
  "businessDeptId": 107,
  "businessTopDeptId": 100
}

### ========================================
### 30. 综合场景测试 - 补账+完整字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430030G0H",
  "customerName": "补账综合测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202401,
  "accountingPeriodEnd": 202412,
  "contactMobile": "***********",
  "contactIdNumber": "350105199006066789",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账综合测试：包含所有字段的完整测试，补全2024年全年账期",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-10-31",
  "initiateDeptId": 2,
  "businessDeptId": 108,
  "businessTopDeptId": 100
}

### ========================================
### 31. 联系人信息验证测试 - 手机号格式验证
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430031I1J",
  "customerName": "联系人信息测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "invalid_mobile",
  "contactIdNumber": "350105199001011234"
}

### ========================================
### 32. 联系人信息验证测试 - 身份证号格式验证
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430032K2L",
  "customerName": "身份证验证测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "contactMobile": "***********",
  "contactIdNumber": "invalid_id_number"
}

### ========================================
### 33. 完整的非标账务测试 - 包含所有子类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430033M3N",
  "customerName": "非标账务完整测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED", "SPECIAL_INDUSTRY", "NON_PROFIT"]
  },
  "requirements": "非标账务完整测试：包含所有子类型",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 109,
  "businessTopDeptId": 100
}

### ========================================
### 34. 日期边界测试 - 最早和最晚日期
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430034O4P",
  "customerName": "日期边界测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "ddl": "2030-12-31"
}

### ========================================
### 35. 税号字段测试 - 税号与信用代码不同
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430035Q5R",
  "customerName": "税号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "accountingInfo": {
    "mainType": "STANDARD"
  }
}
